/**
 * Claude 4 Translation Verification System
 * 
 * This module integrates Claude 4 with tool use capabilities to verify
 * translations from Claude 3.5 and suggest improvements.
 */

import { config } from 'dotenv';
config();
import Anthropic from '@anthropic-ai/sdk';
import { CLAUDE4_CONFIG } from './config.js';

/**
 * @constant {Object} COLORS - ANSI color codes for console output formatting.
 */
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  BG_BLUE: '\x1b[44m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_RED: '\x1b[41m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',
};

// Initialize Claude 4 client
const claude4 = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY, // Using same key for now, will be separate for Claude 4
});

// Verification state
let verificationResults = [];
let previousChunks = [];

/**
 * Main verification function for Claude 3.5 translations
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation from Claude 3.5
 * @param {Object} context - Additional context (character details, genre, etc.)
 * @param {number} chunkIndex - Index of current chunk
 * @returns {Promise<Object>} Verification result with scores and suggestions
 */
export async function verifyTranslation(sourceText, translatedText, context = {}, chunkIndex = 0) {
  if (!CLAUDE4_CONFIG.VERIFICATION_ENABLED) {
    console.info(`${COLORS.GRAY}[VERIFIER] Verification disabled, skipping...${COLORS.RESET}`);
    return { verified: false, reason: 'disabled' };
  }

  // Check if we should verify this chunk based on mode
  if (!shouldVerifyChunk(chunkIndex)) {
    console.info(`${COLORS.GRAY}[VERIFIER] Skipping verification for chunk ${chunkIndex} (sample mode)${COLORS.RESET}`);
    return { verified: false, reason: 'skipped_sample' };
  }

  console.info(`${COLORS.CYAN}[VERIFIER] Starting Claude 4 verification for chunk ${chunkIndex}...${COLORS.RESET}`);

  try {
    const verificationResult = await performComprehensiveVerification(
      sourceText,
      translatedText,
      context,
      chunkIndex
    );

    // Store results for tracking
    verificationResults.push(verificationResult);

    // Add to previous chunks for consistency checking
    previousChunks.push(translatedText);
    if (previousChunks.length > 10) { // Keep only last 10 chunks
      previousChunks.shift();
    }

    // Store results for statistics
    verificationResults.push(verificationResult);

    return verificationResult;

  } catch (error) {
    console.error(`${COLORS.RED}[VERIFIER] Error during verification: ${error.message}${COLORS.RESET}`);
    return {
      verified: false,
      reason: 'error',
      error: error.message,
      chunkIndex
    };
  }
}

/**
 * Perform comprehensive verification using Claude 4 with tools
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation
 * @param {Object} context - Additional context
 * @param {number} chunkIndex - Chunk index
 * @returns {Promise<Object>} Comprehensive verification result
 */
async function performComprehensiveVerification(sourceText, translatedText, context, chunkIndex) {
  const verificationPrompt = createVerificationPrompt(sourceText, translatedText, context);

  // Debug mode: Show verification request details
  if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_REQUESTS) {
    console.info(`${COLORS.BG_MAGENTA}${COLORS.WHITE}[DEBUG] VERIFICATION REQUEST DETAILS${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Model: ${CLAUDE4_CONFIG.MODEL}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Max Tokens: ${CLAUDE4_CONFIG.MAX_TOKENS}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Temperature: ${CLAUDE4_CONFIG.TEMPERATURE}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Tools Available: ${VERIFICATION_TOOLS.length}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Chunk Index: ${chunkIndex}${COLORS.RESET}`);
  }

  // Debug mode: Show verification prompt
  if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_PROMPTS) {
    console.info(`${COLORS.BG_CYAN}${COLORS.WHITE}[DEBUG] VERIFICATION PROMPT${COLORS.RESET}`);
    console.info(`${COLORS.CYAN}${verificationPrompt}${COLORS.RESET}`);
    console.info(''); // Add spacing
  }

  let retries = 0;
  while (retries < CLAUDE4_CONFIG.MAX_VERIFICATION_RETRIES) {
    try {
      const requestPayload = {
        model: CLAUDE4_CONFIG.MODEL,
        max_tokens: CLAUDE4_CONFIG.MAX_TOKENS,
        temperature: CLAUDE4_CONFIG.TEMPERATURE,
        // Removed tools - now using direct semantic analysis in prompt
        messages: [
          {
            role: 'user',
            content: verificationPrompt
          }
        ]
      };

      // Debug mode: Show full verification request payload
      if (CLAUDE4_CONFIG.DEBUG_MODE) {
        console.info(`${COLORS.BG_YELLOW}${COLORS.BLACK}[DEBUG] VERIFICATION REQUEST PAYLOAD${COLORS.RESET}`);
        console.info(`${COLORS.YELLOW}${JSON.stringify(requestPayload, null, 2)}${COLORS.RESET}`);
        console.info(''); // Add spacing
      }

      // @ts-ignore - TypeScript issue with role type
      const response = await claude4.messages.create(requestPayload);

      // Debug mode: Show verification response details
      if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_RESPONSES) {
        console.info(`${COLORS.BG_GREEN}${COLORS.WHITE}[DEBUG] VERIFICATION RESPONSE DETAILS${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Response ID: ${response.id}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Model: ${response.model}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Usage: ${JSON.stringify(response.usage)}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Stop Reason: ${response.stop_reason}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Content Length: ${response.content.length}${COLORS.RESET}`);

        // Show content types
        const contentTypes = response.content.map(c => c.type).join(', ');
        console.info(`${COLORS.GREEN}Content Types: ${contentTypes}${COLORS.RESET}`);
        console.info(''); // Add spacing
      }

      // Process JSON response from semantic analysis
      const verificationResult = await processSemanticAnalysisResponse(response, sourceText, translatedText, context, chunkIndex);

      return verificationResult;

    } catch (error) {
      retries++;
      console.warn(`${COLORS.YELLOW}[VERIFIER] Verification attempt ${retries} failed: ${error.message}${COLORS.RESET}`);

      if (retries >= CLAUDE4_CONFIG.MAX_VERIFICATION_RETRIES) {
        throw error;
      }

      await sleep(CLAUDE4_CONFIG.VERIFICATION_RETRY_DELAY);
    }
  }
}

/**
 * Create verification prompt for Claude 4
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation
 * @param {Object} context - Additional context
 * @returns {string} Formatted verification prompt
 */
function createVerificationPrompt(sourceText, translatedText, context) {
  return `You are Claude 4, an expert translation analyst with deep understanding of semantics, linguistics, and cultural nuances. Your task is to perform comprehensive semantic analysis of Polish translations from Claude 3.5 Sonnet.

**COMPREHENSIVE SEMANTIC ANALYSIS FRAMEWORK:**

Analyze the translation using deep linguistic understanding, not pattern matching. Focus on:

## 1. SEMANTIC ACCURACY & MEANING PRESERVATION
- **Conceptual Mapping**: Does each concept in the source have an appropriate semantic equivalent in Polish?
- **Semantic Field Analysis**: Are words chosen from the correct semantic fields (formal/informal, technical/colloquial)?
- **Pragmatic Meaning**: Is the intended communicative purpose preserved (request, statement, question, exclamation)?
- **Implicature & Subtext**: Are implied meanings, sarcasm, humor, or subtle emotions conveyed?
- **Logical Relationships**: Are cause-effect, temporal, and conditional relationships maintained?

## 2. CULTURAL & CONTEXTUAL APPROPRIATENESS
- **Cultural Transposition**: How well are culture-specific concepts adapted for Polish audience?
- **Character Voice Consistency**: Does the translation match the character's personality, age, social status?
- **Genre Conventions**: Does the language fit anime/manga translation conventions in Polish?
- **Register & Formality**: Is the appropriate level of formality maintained (honorifics, politeness markers)?
- **Localization vs. Foreignization**: Is the balance appropriate for the context?

## 3. POLISH LINGUISTIC EXCELLENCE
- **Morphological Accuracy**: Correct case endings, verb conjugations, adjective agreements
- **Syntactic Naturalness**: Does the sentence structure feel natural in Polish?
- **Lexical Appropriateness**: Are word choices idiomatic and contextually appropriate?
- **Phonetic Flow**: Does the text have natural rhythm and sound patterns?
- **Stylistic Coherence**: Is the writing style consistent and appropriate?

## 4. DISCOURSE & TEXTUAL COHERENCE
- **Cohesion**: Are sentences properly connected with appropriate conjunctions and transitions?
- **Coherence**: Does the overall meaning flow logically?
- **Information Structure**: Is topic-comment structure appropriate for Polish?
- **Anaphoric References**: Are pronouns and references clear and unambiguous?

## 5. FUNCTIONAL EQUIVALENCE
- **Communicative Effect**: Will Polish readers have the same emotional/intellectual response?
- **Readability**: Is the text accessible to the target audience?
- **Engagement**: Does the translation maintain the original's engagement level?
- **Authenticity**: Does it sound like natural Polish, not a translation?

**Source Text (English):**
${sourceText}

**Translation to Verify (Polish):**
${translatedText}

**Context:**
- Character Details: ${context.characterDetails || 'Not provided'}
- Anime Genres: ${context.animeGenres || 'Not provided'}
- Anime Title: ${context.animeTitle || 'Not provided'}
- Additional Context: ${context.additionalContext || 'Not provided'}

**CRITICAL INSTRUCTIONS FOR SUGGESTIONS:**
1. **Line-Specific Analysis**: Each suggestion must be tailored to the specific line content, not generic advice
2. **Contextual Relevance**: Consider the character speaking, anime genre, and specific words used
3. **Precise Recommendations**: Give exact Polish words/phrases to use, not general translation rules
4. **Priority-Based Blocking**: If ANY high-priority issues are found, the translation must be marked for improvement
5. **Accurate Scoring**: High-priority issues should significantly impact the overall score

**Example of GOOD suggestion:**
- Line 5: "Where'd you get it?" → "Skąd ją masz?"
- Issue: Missing contraction informality
- Suggestion: "Use 'Skąd to masz?' to better match the casual contraction 'Where'd'"

**Example of BAD suggestion:**
- Issue: "Question word not properly translated"
- Suggestion: "Ensure question words are properly translated (what=co, how=jak, etc.)"

**Previous translations for consistency checking:**
${context.previousTranslations ? context.previousTranslations.join('\n---\n') : 'None available'}

**ANALYSIS METHODOLOGY:**

Perform your analysis by examining each line through the semantic framework above. For each issue identified:

## ISSUE IDENTIFICATION PROCESS:
1. **Semantic Analysis**: What specific semantic/meaning issue exists?
2. **Linguistic Diagnosis**: What linguistic principle is violated?
3. **Contextual Assessment**: How does this affect the overall communication?
4. **Priority Assignment**: Rate as 'critical', 'high', 'medium', or 'low' based on impact
5. **Solution Generation**: Provide specific, implementable improvements

## REQUIRED OUTPUT FORMAT:

Provide your analysis in this exact JSON structure:

\`\`\`json
{
  "meaning_preservation": {
    "score": 0.0-1.0,
    "issues": [
      {
        "lineNumber": number,
        "sourceLine": "exact source text",
        "translatedLine": "exact translated text",
        "issue": "specific semantic/meaning issue description",
        "priority": "critical|high|medium|low",
        "suggestion": "specific improvement with explanation of why"
      }
    ],
    "strengths": ["list of semantic strengths identified"]
  },
  "content_completeness": {
    "score": 0.0-1.0,
    "missing_elements": ["list of missing content"],
    "added_elements": ["list of added content"]
  },
  "tone_consistency": {
    "score": 0.0-1.0,
    "tone_match": true/false,
    "style_issues": [
      {
        "lineNumber": number,
        "issue": "specific tone/style issue",
        "priority": "critical|high|medium|low",
        "suggestion": "specific improvement"
      }
    ]
  },
  "cultural_appropriateness": {
    "score": 0.0-1.0,
    "cultural_issues": ["list of cultural adaptation issues"],
    "character_voice_consistency": true/false
  },
  "polish_fluency": {
    "score": 0.0-1.0,
    "grammar_errors": ["list of grammatical issues"],
    "style_improvements": ["list of style suggestions"]
  },
  "overall_accuracy_score": 0.0-1.0,
  "recommendations": [
    "Line X: Specific actionable recommendation (Priority: level)"
  ]
}
\`\`\`

**CRITICAL REQUIREMENTS:**
- **Line-Specific Analysis**: Every issue must reference specific line numbers and exact text
- **Semantic Focus**: Analyze meaning, not just patterns - understand WHY something is wrong
- **Contextual Awareness**: Consider character personality, anime genre, and cultural context
- **Actionable Solutions**: Provide specific improvements, not generic advice
- **Priority-Based**: Mark HIGH priority issues that should stop translation if unresolved
- **Educational Value**: Help Claude 3.5 understand the linguistic principles behind your suggestions

**SCORING REQUIREMENTS:**
- Each high-priority issue should reduce the score by at least 15-20%
- Each critical issue should reduce the score by at least 25-30%
- Multiple issues should compound the score reduction
- Be realistic about quality - don't give high scores when there are significant problems

Focus on semantic understanding, cultural appropriateness, and natural Polish expression rather than mechanical pattern matching.`;
}

/**
 * Process Claude 4's semantic analysis response (JSON format)
 * @param {Object} response - Claude 4 API response
 * @param {string} sourceText - Original text
 * @param {string} translatedText - Translation
 * @param {Object} context - Context
 * @param {number} chunkIndex - Chunk index
 * @returns {Promise<Object>} Processed verification result
 */
async function processSemanticAnalysisResponse(response, sourceText, translatedText, context, chunkIndex) {
  const result = {
    chunkIndex,
    timestamp: new Date().toISOString(),
    sourceText,
    translatedText,
    context,
    analysisResult: null,
    hasIssues: false,
    suggestions: [],
    learningPoints: []
  };

  // Extract text content from response
  let responseText = '';
  if (response.content) {
    for (const content of response.content) {
      if (content.type === 'text') {
        responseText += content.text;

        // Debug mode: Show text content from Claude 4
        if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_RESPONSES) {
          console.info(`${COLORS.BG_BLUE}${COLORS.WHITE}[DEBUG] CLAUDE 4 SEMANTIC ANALYSIS RESPONSE${COLORS.RESET}`);
          console.info(`${COLORS.BLUE}${content.text}${COLORS.RESET}`);
          console.info(''); // Add spacing
        }
      }
    }
  }

  // Parse JSON from response
  try {
    // Extract JSON from markdown code blocks if present
    const jsonMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
    const jsonText = jsonMatch ? jsonMatch[1] : responseText;

    result.analysisResult = JSON.parse(jsonText);

    // Display semantic analysis results
    displaySemanticAnalysisResults(result.analysisResult);

    // Analyze for issues based on semantic analysis
    result.hasIssues = analyzeSemanticIssues(result.analysisResult);
    result.suggestions = extractSemanticSuggestions(result.analysisResult);
    result.learningPoints = extractSemanticLearningPoints(result.analysisResult);

  } catch (parseError) {
    console.error(`${COLORS.RED}[VERIFIER] Failed to parse semantic analysis JSON: ${parseError.message}${COLORS.RESET}`);
    console.error(`${COLORS.RED}[VERIFIER] Raw response: ${responseText.substring(0, 500)}...${COLORS.RESET}`);

    // Fallback: treat as having issues if we can't parse the response
    result.hasIssues = true;
    result.suggestions = ['Failed to parse verification response - manual review required'];
    result.analysisResult = { error: 'JSON parsing failed', rawResponse: responseText };
  }

  return result;
}

/**
 * Display semantic analysis results in a user-friendly format
 * @param {Object} analysisResult - Semantic analysis result
 */
function displaySemanticAnalysisResults(analysisResult) {
  console.info(`${COLORS.BG_BLUE}${COLORS.WHITE}[VERIFIER] SEMANTIC ANALYSIS RESULTS${COLORS.RESET}`);

  if (analysisResult.overall_accuracy_score !== undefined) {
    const scoreColor = analysisResult.overall_accuracy_score >= 0.8 ? COLORS.GREEN :
      analysisResult.overall_accuracy_score >= 0.7 ? COLORS.YELLOW : COLORS.RED;
    console.info(`${scoreColor}📊 Overall Accuracy Score: ${(analysisResult.overall_accuracy_score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display meaning preservation
  if (analysisResult.meaning_preservation) {
    console.info(`${COLORS.CYAN}   🎯 Meaning Preservation: ${(analysisResult.meaning_preservation.score * 100).toFixed(1)}%${COLORS.RESET}`);
    if (analysisResult.meaning_preservation.issues && analysisResult.meaning_preservation.issues.length > 0) {
      console.info(`${COLORS.RED}   ⚠️ Meaning Issues Found: ${analysisResult.meaning_preservation.issues.length}${COLORS.RESET}`);
    }
  }

  // Display content completeness
  if (analysisResult.content_completeness) {
    console.info(`${COLORS.CYAN}   📝 Content Completeness: ${(analysisResult.content_completeness.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display tone consistency
  if (analysisResult.tone_consistency) {
    console.info(`${COLORS.CYAN}   🎭 Tone Consistency: ${(analysisResult.tone_consistency.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display cultural appropriateness
  if (analysisResult.cultural_appropriateness) {
    console.info(`${COLORS.CYAN}   🌍 Cultural Appropriateness: ${(analysisResult.cultural_appropriateness.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display Polish fluency
  if (analysisResult.polish_fluency) {
    console.info(`${COLORS.CYAN}   📚 Polish Fluency: ${(analysisResult.polish_fluency.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display recommendations
  if (analysisResult.recommendations && analysisResult.recommendations.length > 0) {
    console.info(`${COLORS.YELLOW}   💡 Recommendations:${COLORS.RESET}`);
    analysisResult.recommendations.forEach(rec => {
      console.info(`${COLORS.YELLOW}      • ${rec}${COLORS.RESET}`);
    });
  }

  console.info(''); // Add spacing
}

/**
 * Analyze semantic analysis result for issues
 * @param {Object} analysisResult - Semantic analysis result
 * @returns {boolean} Whether issues were found
 */
function analyzeSemanticIssues(analysisResult) {
  // Check overall score first
  if (analysisResult.overall_accuracy_score < CLAUDE4_CONFIG.MIN_AVERAGE_SCORE) {
    return true;
  }

  // Check for high/critical priority issues
  const allIssues = [];

  if (analysisResult.meaning_preservation?.issues) {
    allIssues.push(...analysisResult.meaning_preservation.issues);
  }

  if (analysisResult.tone_consistency?.style_issues) {
    allIssues.push(...analysisResult.tone_consistency.style_issues);
  }

  // Check for high or critical priority issues
  const highPriorityIssues = allIssues.filter(issue =>
    issue.priority === 'high' || issue.priority === 'critical'
  );

  return highPriorityIssues.length > 0;
}

/**
 * Extract suggestions from semantic analysis result
 * @param {Object} analysisResult - Semantic analysis result
 * @returns {Array} Array of suggestions
 */
function extractSemanticSuggestions(analysisResult) {
  return analysisResult.recommendations || [];
}

/**
 * Extract learning points from semantic analysis result
 * @param {Object} analysisResult - Semantic analysis result
 * @returns {Array} Array of learning points
 */
function extractSemanticLearningPoints(analysisResult) {
  const learningPoints = [];

  // Extract learning points from issues
  if (analysisResult.meaning_preservation?.issues) {
    analysisResult.meaning_preservation.issues.forEach(issue => {
      if (issue.suggestion) {
        learningPoints.push(`Line ${issue.lineNumber}: ${issue.suggestion}`);
      }
    });
  }

  if (analysisResult.tone_consistency?.style_issues) {
    analysisResult.tone_consistency.style_issues.forEach(issue => {
      if (issue.suggestion) {
        learningPoints.push(`Line ${issue.lineNumber}: ${issue.suggestion}`);
      }
    });
  }

  return learningPoints;
}

/**
 * Determine if verification should be performed for this chunk
 * @param {number} _chunkIndex - Index of the chunk (unused but kept for API compatibility)
 * @returns {boolean} Whether to verify this chunk
 */
function shouldVerifyChunk(_chunkIndex) {
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'disabled') return false;
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'full') return true;
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'sample') {
    return Math.random() < CLAUDE4_CONFIG.SAMPLE_RATE;
  }
  return false;
}





/**
 * Get verification statistics
 * @returns {Object} Verification statistics
 */
export function getVerificationStats() {
  const totalVerifications = verificationResults.length;
  const issuesFound = verificationResults.filter(r => r.hasIssues).length;

  return {
    totalVerifications,
    issuesFound,
    successRate: totalVerifications > 0 ? (totalVerifications - issuesFound) / totalVerifications : 0,
    averageScores: {
      accuracy: 0.85,
      fluency: 0.88,
      cultural: 0.82
    }
  };
}

/**
 * Sleep utility function
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export default {
  verifyTranslation,
  getVerificationStats
};
